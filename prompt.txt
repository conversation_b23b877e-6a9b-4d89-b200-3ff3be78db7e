I am selling a product and i am going to drop you all the ads transcribed for my competitor and make me a better VSL between 2-4 minutes
Note: Don’t forget to mention a visual scene for each one
note: Create logical roadmaps to connect my product’s features to the solution. i will give you a
bad example with heat shoulder product just to understand what i mean Heat → dilates blood
vessels → improves blood flow → flushes waste product from the area & brings nutrients &
oxygen → heals & relaxes stiff muscles → relieves pain
note: I am going to use istockphoto for video clips and animations, and tiktok for clips and to search for my product in use, youtube for clips && to search my product in use, and google veo 2 to generate clips & animations if necesary... ( so i will need you to give me at the end of the VSL script a multiple keywords for each platform to look at and try to find the exact same clip for each part (because with 1 keyword each platform it's hard to find, sometimes you need to use multiple keywords, sentence etc...)) and for google veo you will need to generate me a prompt for example check the prompt for animations inside fitness_pain_relief_animations.json and the prompt for clips inside of google_veo2_basic_text_to_video.json  
note: for the all the transcribed ads will be inside videos folder in a txt format with all their mp4 video ads
note: later on i will ask you for 6 different hooks focused around different angles to replace the opening scene but for the same VSL script

note: before you start generating you can rename videos names and the transcribed txt of each one of them to the same name to make it understand the context 
note: mention me in the chat what framework is used for each one of them...
note: remember the product in use it's going to be either on facebook, tiktok, youtube, or my competitor clips... not in istockvideo or generated by google veo 2 so when there is clips where i need to show the products in use or anything just mention it and give me keywords to search for the product in use in youtube, tiktok, facebook 
note: i will even need a clips to look for for each sentence, people aren't going to wait. i need to show every piece that is being talked about otherwise people will get bored... so it's will be like 10 clips each scene like scene 1 (clip1: 2sec, clip2: 5sec, clip3: 2sec, clip4: 5sec, clip5: 1sec, clip6: 3sec. etc... )
note: The market sophistication is in stage 3, so introduce a new mechanism and make stronger claims and
stronger desires advanced features... because this product has been advertised, and it is being advertised
by many competitors
